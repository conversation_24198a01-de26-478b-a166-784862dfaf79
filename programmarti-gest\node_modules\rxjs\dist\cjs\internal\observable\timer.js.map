{"version": 3, "file": "timer.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/timer.ts"], "names": [], "mappings": ";;;AAAA,4CAA2C;AAE3C,4CAA6D;AAC7D,mDAAkD;AAClD,yCAA6C;AAgI7C,SAAgB,KAAK,CACnB,OAA0B,EAC1B,mBAA4C,EAC5C,SAAyC;IAFzC,wBAAA,EAAA,WAA0B;IAE1B,0BAAA,EAAA,YAA2B,aAAc;IAIzC,IAAI,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAI,mBAAmB,IAAI,IAAI,EAAE;QAI/B,IAAI,yBAAW,CAAC,mBAAmB,CAAC,EAAE;YACpC,SAAS,GAAG,mBAAmB,CAAC;SACjC;aAAM;YAGL,gBAAgB,GAAG,mBAAmB,CAAC;SACxC;KACF;IAED,OAAO,IAAI,uBAAU,CAAC,UAAC,UAAU;QAI/B,IAAI,GAAG,GAAG,oBAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,SAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;QAEvE,IAAI,GAAG,GAAG,CAAC,EAAE;YAEX,GAAG,GAAG,CAAC,CAAC;SACT;QAGD,IAAI,CAAC,GAAG,CAAC,CAAC;QAGV,OAAO,SAAS,CAAC,QAAQ,CAAC;YACxB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;gBAEtB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;gBAErB,IAAI,CAAC,IAAI,gBAAgB,EAAE;oBAGzB,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;iBAC5C;qBAAM;oBAEL,UAAU,CAAC,QAAQ,EAAE,CAAC;iBACvB;aACF;QACH,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC,CAAC,CAAC;AACL,CAAC;AArDD,sBAqDC"}