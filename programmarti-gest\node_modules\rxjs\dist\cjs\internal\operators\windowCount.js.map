{"version": 3, "file": "windowCount.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/windowCount.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AACA,sCAAqC;AAErC,qCAAuC;AACvC,2DAAgE;AA+DhE,SAAgB,WAAW,CAAI,UAAkB,EAAE,gBAA4B;IAA5B,iCAAA,EAAA,oBAA4B;IAC7E,IAAM,UAAU,GAAG,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC;IAExE,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,OAAO,GAAG,CAAC,IAAI,iBAAO,EAAK,CAAC,CAAC;QACjC,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,IAAI,KAAK,GAAG,CAAC,CAAC;QAGd,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC;QAE3C,MAAM,CAAC,SAAS,CACd,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAQ;;;gBAIP,KAAqB,IAAA,YAAA,SAAA,OAAO,CAAA,gCAAA,qDAAE;oBAAzB,IAAM,QAAM,oBAAA;oBACf,QAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACpB;;;;;;;;;YAMD,IAAM,CAAC,GAAG,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE;gBAClC,OAAO,CAAC,KAAK,EAAG,CAAC,QAAQ,EAAE,CAAC;aAC7B;YAOD,IAAI,EAAE,KAAK,GAAG,UAAU,KAAK,CAAC,EAAE;gBAC9B,IAAM,QAAM,GAAG,IAAI,iBAAO,EAAK,CAAC;gBAChC,OAAO,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;gBACrB,UAAU,CAAC,IAAI,CAAC,QAAM,CAAC,YAAY,EAAE,CAAC,CAAC;aACxC;QACH,CAAC,EACD;YACE,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,QAAQ,EAAE,CAAC;aAC7B;YACD,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,EACD,UAAC,GAAG;YACF,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC7B;YACD,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,EACD;YACE,MAAM,GAAG,IAAK,CAAC;YACf,OAAO,GAAG,IAAK,CAAC;QAClB,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AA7DD,kCA6DC"}